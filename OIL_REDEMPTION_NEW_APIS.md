# 油品赎回新增接口文档

## 接口概述

新增了两个接口：
1. 按提交时间分组查询接口 - 用于查询用户的申请并按时间分组
2. 批量审批接口 - 用于批量审批指定时间的所有待审批申请

## 接口详情

### 1. 按提交时间分组查询接口

**接口地址**: `GET /exam/api/oil/redemption/timeGroup/{userId}`

**请求方式**: GET

**接口描述**: 查询指定用户的油品赎回申请，按提交时间分组并汇总审批状态

#### 请求参数

| 参数名 | 参数类型 | 是否必填 | 参数描述 |
|--------|----------|----------|----------|
| userId | String   | 是       | 用户ID |

#### 请求示例

```bash
curl -X GET "http://localhost:8080/exam/api/oil/redemption/timeGroup/user123"
```

#### 响应参数

| 参数名 | 参数类型 | 参数描述 |
|--------|----------|----------|
| code   | Integer  | 响应状态码，0表示成功 |
| msg    | String   | 响应消息 |
| data   | Array    | 时间分组数据列表 |

**data数组中每个对象的结构**:

| 参数名 | 参数类型 | 参数描述 |
|--------|----------|----------|
| tiJiaoShiJian | String | 提交时间 |
| keHuShenPiZhuangTai | String | 汇总审批状态（"待审批"或"已审批"） |
| count | Long | 该时间下的记录总数 |
| pendingCount | Long | 该时间下待审批的记录数 |

#### 成功响应示例

```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "tiJiaoShiJian": "2024-01-02",
      "keHuShenPiZhuangTai": "已审批",
      "count": 3,
      "pendingCount": 0
    },
    {
      "tiJiaoShiJian": "2024-01-01",
      "keHuShenPiZhuangTai": "待审批",
      "count": 5,
      "pendingCount": 2
    }
  ]
}
```

#### 业务逻辑

1. 查询指定用户的所有未删除记录
2. 按提交时间（tiJiaoShiJian）进行分组
3. 对每个时间组：
   - 统计总记录数
   - 统计待审批记录数
   - 如果存在待审批记录，汇总状态为"待审批"
   - 如果所有记录都不是待审批，汇总状态为"已审批"
4. 按提交时间倒序排序返回

---

### 2. 批量审批接口

**接口地址**: `POST /exam/api/oil/redemption/batchApprove`

**请求方式**: POST

**接口描述**: 批量审批指定用户在指定时间提交的所有待审批申请

#### 请求参数

| 参数名 | 参数类型 | 是否必填 | 参数描述 |
|--------|----------|----------|----------|
| userId | String   | 是       | 用户ID |
| tiJiaoShiJian | String | 是 | 提交时间 |

#### 请求示例

```bash
curl -X POST "http://localhost:8080/exam/api/oil/redemption/batchApprove" \
     -d "userId=user123&tiJiaoShiJian=2024-01-01"
```

#### 响应参数

| 参数名 | 参数类型 | 参数描述 |
|--------|----------|----------|
| code   | Integer  | 响应状态码，0表示成功 |
| msg    | String   | 响应消息 |
| data   | String   | 操作结果描述 |

#### 成功响应示例

```json
{
  "code": 0,
  "msg": "success",
  "data": "批量审批成功，共审批了3条记录"
}
```

#### 失败响应示例

```json
{
  "code": 0,
  "msg": "success",
  "data": "批量审批失败: 具体错误信息"
}
```

#### 业务逻辑

1. 根据用户ID和提交时间查找所有符合条件的记录
2. 筛选出状态为"待审批"且未删除的记录
3. 批量更新这些记录：
   - 将 `keHuShenPiZhuangTai` 更新为"已审批"
   - 将 `liuChengJinDu` 更新为"已审批"
   - 更新 `updateTime` 为当前时间
4. 返回实际更新的记录数

## 使用场景

### 场景1: 查看申请状态概览
前端可以调用时间分组接口，展示用户各个时间段的申请状态汇总，方便用户快速了解哪些时间段的申请还需要处理。

### 场景2: 批量操作
当用户看到某个时间段有待审批的申请时，可以一键批量审批该时间段的所有申请，提高操作效率。

## 注意事项

1. **权限控制**: 建议添加权限验证，确保用户只能查看和操作自己的申请
2. **数据一致性**: 批量审批操作是原子性的，要么全部成功，要么全部失败
3. **日志记录**: 所有操作都会记录详细日志，便于审计和问题排查
4. **时间格式**: 提交时间格式为"yyyy-MM-dd"
5. **排序规则**: 时间分组结果按提交时间倒序排列（最新的在前）

## 相关接口

- 创建申请: `POST /exam/api/oil/redemption/save`
- 查询申请详情: `GET /exam/api/oil/redemption/detail/{id}`
- 查询申请列表: `GET /exam/api/oil/redemption/getList/{user_id}`
- 撤回申请: `POST /exam/api/oil/redemption/withdraw/{id}`

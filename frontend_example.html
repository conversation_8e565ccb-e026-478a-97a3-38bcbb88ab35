<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>油品赎回管理示例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .time-group { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .pending { background-color: #fff3cd; border-color: #ffeaa7; }
        .approved { background-color: #d4edda; border-color: #c3e6cb; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn:hover { opacity: 0.8; }
        .status { font-weight: bold; }
        .pending-status { color: #856404; }
        .approved-status { color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>油品赎回申请管理</h1>
        
        <div>
            <label for="userId">用户ID:</label>
            <input type="text" id="userId" value="user123" />
            <button class="btn btn-primary" onclick="loadTimeGroups()">查询时间分组</button>
        </div>
        
        <div id="timeGroups"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/exam/api/oil/redemption';
        
        // 加载时间分组数据
        async function loadTimeGroups() {
            const userId = document.getElementById('userId').value;
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/timeGroup/${userId}`);
                const result = await response.json();
                
                if (result.code === 0) {
                    displayTimeGroups(result.data);
                } else {
                    alert('查询失败: ' + result.msg);
                }
            } catch (error) {
                console.error('查询失败:', error);
                alert('查询失败: ' + error.message);
            }
        }
        
        // 显示时间分组数据
        function displayTimeGroups(timeGroups) {
            const container = document.getElementById('timeGroups');
            
            if (!timeGroups || timeGroups.length === 0) {
                container.innerHTML = '<p>暂无数据</p>';
                return;
            }
            
            let html = '<h2>申请时间分组</h2>';
            
            timeGroups.forEach(group => {
                const isPending = group.keHuShenPiZhuangTai === '待审批';
                const statusClass = isPending ? 'pending' : 'approved';
                const statusTextClass = isPending ? 'pending-status' : 'approved-status';
                
                html += `
                    <div class="time-group ${statusClass}">
                        <h3>提交时间: ${group.tiJiaoShiJian}</h3>
                        <p>
                            <span class="status ${statusTextClass}">状态: ${group.keHuShenPiZhuangTai}</span>
                            <br>
                            总记录数: ${group.count} 条
                            <br>
                            待审批记录数: ${group.pendingCount} 条
                        </p>
                        ${isPending ? `
                            <button class="btn btn-success" 
                                    onclick="batchApprove('${document.getElementById('userId').value}', '${group.tiJiaoShiJian}')">
                                批量审批 (${group.pendingCount} 条)
                            </button>
                        ` : ''}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 批量审批
        async function batchApprove(userId, tiJiaoShiJian) {
            if (!confirm(`确定要批量审批 ${tiJiaoShiJian} 的所有待审批申请吗？`)) {
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('userId', userId);
                formData.append('tiJiaoShiJian', tiJiaoShiJian);
                
                const response = await fetch(`${API_BASE}/batchApprove`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 0) {
                    alert(result.data);
                    // 重新加载数据
                    loadTimeGroups();
                } else {
                    alert('批量审批失败: ' + result.msg);
                }
            } catch (error) {
                console.error('批量审批失败:', error);
                alert('批量审批失败: ' + error.message);
            }
        }
        
        // 页面加载时自动查询
        window.onload = function() {
            loadTimeGroups();
        };
    </script>
</body>
</html>

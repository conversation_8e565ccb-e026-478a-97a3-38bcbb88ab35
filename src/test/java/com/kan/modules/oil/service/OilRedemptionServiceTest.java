package com.kan.modules.oil.service;

import com.kan.modules.oil.dto.OilRedemptionTimeGroupDTO;
import com.kan.modules.oil.entity.OilRedemptions;
import com.kan.modules.oil.service.impl.OilRedemptionServiceImpl;
import com.kan.modules.sys.service.EmailService;
import com.kan.modules.sys.service.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 油品赎回服务测试类
 */
public class OilRedemptionServiceTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private RedisService redisService;

    @Mock
    private EmailService emailService;

    @InjectMocks
    private OilRedemptionServiceImpl oilRedemptionService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testWithdrawSuccess() {
        // 准备测试数据
        String testId = "test123";
        OilRedemptions redemption = new OilRedemptions();
        redemption.setId(testId);
        redemption.setKeHuShenPiZhuangTai("待审批");
        redemption.setKeHuMingChen("测试客户");
        redemption.setYouPinZhongLei("92#汽油");
        redemption.setShuYouShuLiang(new BigDecimal("100"));
        redemption.setShuYouJinE(new BigDecimal("800"));

        // Mock方法调用
        when(mongoTemplate.findOne(any(Query.class), eq(OilRedemptions.class)))
                .thenReturn(redemption);
        when(mongoTemplate.updateFirst(any(Query.class), any(Update.class), eq(OilRedemptions.class)))
                .thenReturn(null);

        // 执行测试
        boolean result = oilRedemptionService.withdraw(testId);

        // 验证结果
        assertTrue(result);
        verify(mongoTemplate, times(1)).updateFirst(any(Query.class), any(Update.class), eq(OilRedemptions.class));
        // 注意：由于邮件发送是异步的，这里不验证邮件发送
    }

    @Test
    void testWithdrawNotFound() {
        // 准备测试数据
        String testId = "notfound";

        // Mock方法调用
        when(mongoTemplate.findOne(any(Query.class), eq(OilRedemptions.class)))
                .thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            oilRedemptionService.withdraw(testId);
        });

        assertTrue(exception.getMessage().contains("预约信息不存在"));
    }

    @Test
    void testWithdrawInvalidStatus() {
        // 准备测试数据
        String testId = "test123";
        OilRedemptions redemption = new OilRedemptions();
        redemption.setId(testId);
        redemption.setKeHuShenPiZhuangTai("已审批"); // 非待审批状态

        // Mock方法调用
        when(mongoTemplate.findOne(any(Query.class), eq(OilRedemptions.class)))
                .thenReturn(redemption);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            oilRedemptionService.withdraw(testId);
        });

        assertTrue(exception.getMessage().contains("当前状态不允许撤回"));
    }

    @Test
    void testGetTimeGroupList() {
        // 准备测试数据
        String userId = "user123";

        OilRedemptions redemption1 = new OilRedemptions();
        redemption1.setTiJiaoShiJian("2024-01-01");
        redemption1.setKeHuShenPiZhuangTai("待审批");

        OilRedemptions redemption2 = new OilRedemptions();
        redemption2.setTiJiaoShiJian("2024-01-01");
        redemption2.setKeHuShenPiZhuangTai("已审批");

        OilRedemptions redemption3 = new OilRedemptions();
        redemption3.setTiJiaoShiJian("2024-01-02");
        redemption3.setKeHuShenPiZhuangTai("已审批");

        List<OilRedemptions> mockData = Arrays.asList(redemption1, redemption2, redemption3);

        // Mock方法调用
        when(mongoTemplate.find(any(Query.class), eq(OilRedemptions.class)))
                .thenReturn(mockData);

        // 执行测试
        List<OilRedemptionTimeGroupDTO> result = oilRedemptionService.getTimeGroupList(userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个时间组（2024-01-02，全部已审批）
        OilRedemptionTimeGroupDTO group1 = result.get(0);
        assertEquals("2024-01-02", group1.getTiJiaoShiJian());
        assertEquals("已审批", group1.getKeHuShenPiZhuangTai());
        assertEquals(1L, group1.getCount());
        assertEquals(0L, group1.getPendingCount());

        // 验证第二个时间组（2024-01-01，存在待审批）
        OilRedemptionTimeGroupDTO group2 = result.get(1);
        assertEquals("2024-01-01", group2.getTiJiaoShiJian());
        assertEquals("待审批", group2.getKeHuShenPiZhuangTai());
        assertEquals(2L, group2.getCount());
        assertEquals(1L, group2.getPendingCount());
    }

    @Test
    void testBatchApprove() {
        // 准备测试数据
        String userId = "user123";
        String tiJiaoShiJian = "2024-01-01";

        // Mock方法调用
        when(mongoTemplate.updateMulti(any(Query.class), any(Update.class), eq(OilRedemptions.class)))
                .thenReturn(org.springframework.data.mongodb.core.query.UpdateResult.acknowledged(2, 2L, null));

        // 执行测试
        int result = oilRedemptionService.batchApprove(userId, tiJiaoShiJian);

        // 验证结果
        assertEquals(2, result);
        verify(mongoTemplate, times(1)).updateMulti(any(Query.class), any(Update.class), eq(OilRedemptions.class));
    }
}

package com.kan.modules.sys.service;

import com.kan.modules.sys.service.impl.EmailServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 邮件服务测试类
 */
public class EmailServiceTest {

    @Mock
    private JavaMailSender mailSender;

    @Mock
    private SysUserService sysUserService;

    @InjectMocks
    private EmailServiceImpl emailService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置发送邮箱地址
        ReflectionTestUtils.setField(emailService, "fromEmail", "<EMAIL>");
    }

    @Test
    void testSendSimpleEmail() {
        // 准备测试数据
        String to = "<EMAIL>";
        String subject = "测试邮件";
        String content = "这是一封测试邮件";

        // 执行测试
        emailService.sendSimpleEmail(to, subject, content);

        // 验证邮件发送器被调用
        verify(mailSender, times(1)).send(any(SimpleMailMessage.class));
    }

    @Test
    void testSendHtmlEmail() {
        // 准备测试数据
        String to = "<EMAIL>";
        String subject = "HTML测试邮件";
        String content = "<h1>这是HTML邮件</h1><p>测试内容</p>";

        // Mock MimeMessage
        when(mailSender.createMimeMessage()).thenReturn(mock(javax.mail.internet.MimeMessage.class));

        // 执行测试
        emailService.sendHtmlEmail(to, subject, content);

        // 验证邮件发送器被调用
        verify(mailSender, times(1)).send(any(javax.mail.internet.MimeMessage.class));
    }

    @Test
    void testSendEmailToAdminWithDefaultEmail() {
        // 准备测试数据
        String subject = "管理员通知";
        String content = "这是发送给管理员的邮件";

        // Mock管理员用户不存在的情况
        when(sysUserService.getById(1L)).thenReturn(null);

        // 执行测试
        emailService.sendEmailToAdmin(subject, content);

        // 验证邮件发送器被调用（发送到默认管理员邮箱）
        verify(mailSender, times(1)).send(any(SimpleMailMessage.class));
    }
}

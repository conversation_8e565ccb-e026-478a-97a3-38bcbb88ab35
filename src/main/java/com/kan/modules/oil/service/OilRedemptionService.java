package com.kan.modules.oil.service;

import com.kan.modules.oil.entity.OilRedemptions;
import com.kan.modules.oil.entity.OilRedemptions;

import java.util.List;

/**
 * 油品赎回预约服务接口
 */
public interface OilRedemptionService {

    /**
     * 新增油品赎回预约
     * @param reqDTO 请求参数
     * @return 预约ID
     */
    String save(OilRedemptions reqDTO);

    /**
     * 根据ID查询油品赎回预约详情
     * @param id 预约ID
     * @return 预约详情
     */
    OilRedemptions findById(String id);

    /**
     * 根据ID查询油品赎回预约实体
     * @param id 预约ID
     * @return 预约实体
     */
    OilRedemptions getById(String id);

    /**
     * 查询指定用户创建的预约列表
     * @param userId 用户ID
     * @return 预约列表，按提交时间倒序排序
     */
    List<OilRedemptions> selectList(String userId);



}

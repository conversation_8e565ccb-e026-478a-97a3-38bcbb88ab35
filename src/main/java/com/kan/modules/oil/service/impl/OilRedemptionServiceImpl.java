package com.kan.modules.oil.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kan.common.exception.KanException;
import com.kan.modules.oil.dto.OilRedemptionTimeGroupDTO;
import com.kan.modules.oil.entity.OilRedemptions;
import com.kan.modules.oil.service.OilRedemptionService;
import com.kan.modules.sys.dto.MongoUser;
import com.kan.modules.sys.service.EmailService;
import com.kan.modules.sys.service.RedisService;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 油品赎回预约服务实现类
 */
@Slf4j
@Service
public class OilRedemptionServiceImpl implements OilRedemptionService {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RedisService redisService;
    @Autowired
    private EmailService emailService;

    @Override
    public String save(OilRedemptions reservation) {
        MongoUser mongoUser = JSONObject.parseObject(redisService.get("user_info_by_id_" + reservation.getKeHuid()).toString(), MongoUser.class);
        try {
            // 生成预约ID
            String reservationId = IdWorker.getIdStr();
            // 创建实体对象
            reservation.setId(reservationId);
            // 设置时间信息
            Date now = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            // 设置审批状态为待审批，审核状态为待审核
            reservation.setTiJiaoShiJian(sdf.format(now));
            reservation.setKeHuShenPiZhuangTai("待审批");
            reservation.setLiuChengJinDu("待审批");
            reservation.setKeHuMingChen(mongoUser.getYongHuXingMing());
            reservation.setKeHuid(mongoUser.getDataId());
            reservation.setDeptId(mongoUser.getDeptId());
            reservation.setModelId("444d5f916bfda0c7d4199fdde00aee5eed167c");
            // 设置删除标志
            reservation.setDelFlag(false);
            // 保存到MongoDB
            mongoTemplate.save(reservation);
            
            log.info("油品赎回预约保存成功，预约ID: {}", reservationId);
            return reservationId;
            
        } catch (Exception e) {
            log.error("保存油品赎回预约失败", e);
            throw new KanException("保存油品赎回预约失败: " + e.getMessage());
        }
    }

    @Override
    public OilRedemptions findById(String id) {
        try {
            OilRedemptions reservation = getById(id);
            if (reservation == null) {
                return null;
            }
            
            return reservation;
            
        } catch (Exception e) {
            log.error("查询油品赎回预约失败，ID: {}", id, e);
            throw new KanException("查询油品赎回预约失败: " + e.getMessage());
        }
    }

    @Override
    public OilRedemptions getById(String id) {
        try {
            Query query = new Query(Criteria.where("id").is(id).and("delFlag").ne(true));
            return mongoTemplate.findOne(query, OilRedemptions.class);
        } catch (Exception e) {
            log.error("根据ID查询油品赎回预约失败，ID: {}", id, e);
            throw new KanException("查询油品赎回预约失败: " + e.getMessage());
        }
    }

    @Override
    public List<OilRedemptions> selectList(String userId) {
        try {
            // 构建查询条件
            Query query = new Query();
            query.addCriteria(Criteria.where("keHuid").is(userId)
                    .and("delFlag").ne(true));

            // 按提交时间倒序排序
            query.with(Sort.by(Sort.Direction.DESC, "tiJiaoShiJian"));

            // 查询数据
            List<OilRedemptions> reservations = mongoTemplate.find(query, OilRedemptions.class);


            log.info("查询用户{}的预约列表成功，共{}条记录", userId, reservations.size());
            return reservations;

        } catch (Exception e) {
            log.error("查询用户{}的预约列表失败", userId, e);
            throw new KanException("查询预约列表失败: " + e.getMessage());
        }
    }

    @Override
    public boolean withdraw(String id) {
        try {
            // 查询预约信息
            OilRedemptions redemption = getById(id);
            if (redemption == null) {
                throw new KanException("预约信息不存在");
            }

            // 检查当前状态是否可以撤回
            if (!"待审批".equals(redemption.getKeHuShenPiZhuangTai())) {
                throw new KanException("当前状态不允许撤回，只有待审批状态的申请才能撤回");
            }

            // 更新状态为已撤回
            Query query = new Query(Criteria.where("id").is(id).and("delFlag").ne(true));
            Update update = new Update();
            update.set("keHuShenPiZhuangTai", "已撤回");
            update.set("liuChengJinDu", "已撤回");
            update.set("updateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            mongoTemplate.updateFirst(query, update, OilRedemptions.class);

            // 发送邮件通知管理员
            String subject = "油品赎回申请撤回通知";
            String content = String.format(
                "尊敬的管理员：\n\n" +
                "有一笔油品赎回申请已被撤回，详情如下：\n" +
                "申请ID: %s\n" +
                "客户名称: %s\n" +
                "油品种类: %s\n" +
                "赎油数量: %s\n" +
                "赎油金额: %s\n" +
                "撤回时间: %s\n\n" +
                "请及时处理相关事务。\n\n" +
                "系统自动发送，请勿回复。",
                redemption.getId(),
                redemption.getKeHuMingChen(),
                redemption.getYouPinZhongLei(),
                redemption.getShuYouShuLiang(),
                redemption.getShuYouJinE(),
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
            );

            // 异步发送邮件，避免邮件发送失败影响主业务
            new Thread(() -> {
                try {
                    emailService.sendEmailToAdmin(subject, content);
                } catch (Exception e) {
                    log.error("发送撤回通知邮件失败", e);
                }
            }).start();

            log.info("油品赎回预约撤回成功，预约ID: {}", id);
            return true;

        } catch (Exception e) {
            log.error("撤回油品赎回预约失败，ID: {}", id, e);
            throw new KanException("撤回预约失败: " + e.getMessage());
        }
    }

    @Override
    public List<OilRedemptionTimeGroupDTO> getTimeGroupList(String userId) {
        try {
            // 查询该用户的所有未删除记录
            Query query = new Query();
            query.addCriteria(Criteria.where("keHuid").is(userId)
                    .and("delFlag").ne(true));

            List<OilRedemptions> allRedemptions = mongoTemplate.find(query, OilRedemptions.class);

            // 按提交时间分组
            Map<String, List<OilRedemptions>> groupedByTime = allRedemptions.stream()
                    .collect(Collectors.groupingBy(OilRedemptions::getTiJiaoShiJian));

            // 转换为DTO列表
            List<OilRedemptionTimeGroupDTO> result = new ArrayList<>();
            for (Map.Entry<String, List<OilRedemptions>> entry : groupedByTime.entrySet()) {
                String tiJiaoShiJian = entry.getKey();
                List<OilRedemptions> redemptions = entry.getValue();

                // 统计该时间下的记录数和待审批记录数
                long totalCount = redemptions.size();
                long pendingCount = redemptions.stream()
                        .mapToLong(r -> "待审批".equals(r.getKeHuShenPiZhuangTai()) ? 1 : 0)
                        .sum();

                // 确定汇总状态：如果存在待审批的记录，则返回"待审批"，否则返回"已审批"
                String aggregatedStatus = pendingCount > 0 ? "待审批" : "已审批";

                OilRedemptionTimeGroupDTO dto = new OilRedemptionTimeGroupDTO(
                        tiJiaoShiJian, aggregatedStatus, totalCount, pendingCount);
                result.add(dto);
            }

            // 按提交时间倒序排序
            result.sort((a, b) -> b.getTiJiaoShiJian().compareTo(a.getTiJiaoShiJian()));

            log.info("查询用户{}的时间分组列表成功，共{}个时间组", userId, result.size());
            return result;

        } catch (Exception e) {
            log.error("查询用户{}的时间分组列表失败", userId, e);
            throw new KanException("查询时间分组列表失败: " + e.getMessage());
        }
    }

    @Override
    public int batchApprove(String userId, String tiJiaoShiJian) {
        try {
            // 构建查询条件：指定用户ID、提交时间、状态为待审批、未删除
            Query query = new Query();
            query.addCriteria(Criteria.where("keHuid").is(userId)
                    .and("tiJiaoShiJian").is(tiJiaoShiJian)
                    .and("keHuShenPiZhuangTai").is("待审批")
                    .and("delFlag").ne(true));

            // 构建更新操作
            Update update = new Update();
            update.set("keHuShenPiZhuangTai", "已审批");
            update.set("updateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            // 执行批量更新
            UpdateResult updateResult = mongoTemplate.updateMulti(query, update, OilRedemptions.class);
            long modifiedCount = updateResult.getModifiedCount();

            log.info("批量审批成功，用户ID: {}, 提交时间: {}, 更新记录数: {}", userId, tiJiaoShiJian, modifiedCount);
            return (int) modifiedCount;

        } catch (Exception e) {
            log.error("批量审批失败，用户ID: {}, 提交时间: {}", userId, tiJiaoShiJian, e);
            throw new KanException("批量审批失败: " + e.getMessage());
        }
    }
}

package com.kan.modules.oil.controller;

import com.kan.common.utils.Result;
import com.kan.core.api.dto.BaseIdRespDTO;
import com.kan.modules.oil.entity.OilRedemptions;
import com.kan.modules.oil.service.OilRedemptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@Slf4j
@Api(tags = {"油品赎回"})
@RestController
@RequestMapping("/exam/api/oil/redemption")
public class OilRedemptionController {
    @Autowired
    private OilRedemptionService oilRedemptionService;

    /**
     * 新增油品赎回预约
     * @param reqDTO 请求参数
     * @return 预约ID
     */
    @ApiOperation(value = "新增油品赎回预约", notes = "创建新的油品赎回预约申请")
    @PostMapping("/save")
    @CrossOrigin
    public Result save(@RequestBody OilRedemptions reqDTO) {
        try {
            // 保存预约
            String reservationId = oilRedemptionService.save(reqDTO);

            log.info("油品赎回预约创建成功，预约ID: {}", reservationId);
            return new Result().appOk(new BaseIdRespDTO(reservationId));

        } catch (Exception e) {
            log.error("新增油品赎回预约失败", e);
            return new Result().appOk("新增油品赎回预约失败: " + e.getMessage());
        }
    }

    /**
     * 查询油品赎回预约详情
     * @param id 预约ID
     * @return 预约详情
     */
    @ApiOperation(value = "查询油品赎回预约详情", notes = "根据预约ID查询预约详情")
    @GetMapping("/detail/{id}")
    @CrossOrigin
    public Result detail(@PathVariable("id") String id) {
        try {
            OilRedemptions respDTO = oilRedemptionService.findById(id);
            if (respDTO == null) {
                return new Result().appOk("预约信息不存在");
            }

            return new Result().appOk(respDTO);

        } catch (Exception e) {
            log.error("查询油品赎回预约详情失败，ID: {}", id, e);
            return new Result().appOk("查询预约详情失败: " + e.getMessage());
        }
    }
    /**
     * 查询油品赎回预约列表
     * @return 预约列表
     */
    @ApiOperation(value = "查询油品赎回预约详情", notes = "根据预约ID查询预约详情")
    @GetMapping("/getList/{user_id}")
    @CrossOrigin
    public Result getList(@PathVariable("user_id") String id) {
        List<OilRedemptions> respDTO = oilRedemptionService.selectList(id);
        return new Result().appOk(respDTO);
    }

}

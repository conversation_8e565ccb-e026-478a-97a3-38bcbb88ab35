package com.kan.modules.sys.controller;

import com.kan.common.utils.Result;
import com.kan.modules.sys.service.EmailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 邮件测试控制器
 * 用于测试邮件发送功能
 */
@Slf4j
@Api(tags = {"邮件测试"})
@RestController
@RequestMapping("/sys/email/test")
public class EmailTestController {

    @Autowired
    private EmailService emailService;

    /**
     * 发送测试邮件
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果
     */
    @ApiOperation(value = "发送测试邮件", notes = "发送简单文本邮件进行测试")
    @PostMapping("/sendSimple")
    @CrossOrigin
    public Result sendSimpleEmail(@RequestParam("to") String to,
                                 @RequestParam("subject") String subject,
                                 @RequestParam("content") String content) {
        try {
            emailService.sendSimpleEmail(to, subject, content);
            log.info("测试邮件发送成功 - 收件人: {}", to);
            return Result.ok("邮件发送成功");

        } catch (Exception e) {
            log.error("测试邮件发送失败 - 收件人: {}", to, e);
            return Result.error("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTML测试邮件
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content HTML内容
     * @return 发送结果
     */
    @ApiOperation(value = "发送HTML测试邮件", notes = "发送HTML格式邮件进行测试")
    @PostMapping("/sendHtml")
    @CrossOrigin
    public Result sendHtmlEmail(@RequestParam("to") String to,
                               @RequestParam("subject") String subject,
                               @RequestParam("content") String content) {
        try {
            emailService.sendHtmlEmail(to, subject, content);
            log.info("HTML测试邮件发送成功 - 收件人: {}", to);
            return Result.ok("HTML邮件发送成功");

        } catch (Exception e) {
            log.error("HTML测试邮件发送失败 - 收件人: {}", to, e);
            return Result.error("HTML邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送邮件给管理员
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果
     */
    @ApiOperation(value = "发送邮件给管理员", notes = "测试发送邮件给管理员功能")
    @PostMapping("/sendToAdmin")
    @CrossOrigin
    public Result sendEmailToAdmin(@RequestParam("subject") String subject,
                                  @RequestParam("content") String content) {
        try {
            emailService.sendEmailToAdmin(subject, content);
            log.info("管理员邮件发送成功");
            return Result.ok("管理员邮件发送成功");

        } catch (Exception e) {
            log.error("管理员邮件发送失败", e);
            return Result.error("管理员邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送油品赎回撤回通知测试邮件
     * @return 发送结果
     */
    @ApiOperation(value = "测试撤回通知邮件", notes = "发送油品赎回撤回通知的测试邮件")
    @PostMapping("/testWithdrawNotification")
    @CrossOrigin
    public Result testWithdrawNotification() {
        try {
            String subject = "油品赎回申请撤回通知（测试）";
            String content = String.format(
                "尊敬的管理员：\n\n" +
                "有一笔油品赎回申请已被撤回，详情如下：\n" +
                "申请ID: %s\n" +
                "客户名称: %s\n" +
                "油品种类: %s\n" +
                "赎油数量: %s\n" +
                "赎油金额: %s\n" +
                "撤回时间: %s\n\n" +
                "请及时处理相关事务。\n\n" +
                "系统自动发送，请勿回复。\n\n" +
                "注：这是一封测试邮件。",
                "TEST123456",
                "测试客户",
                "92#汽油",
                "100.00吨",
                "800.00元",
                "2024-01-15 10:30:00"
            );

            emailService.sendEmailToAdmin(subject, content);
            log.info("撤回通知测试邮件发送成功");
            return Result.ok("撤回通知测试邮件发送成功");

        } catch (Exception e) {
            log.error("撤回通知测试邮件发送失败", e);
            return Result.error("撤回通知测试邮件发送失败: " + e.getMessage());
        }
    }
}

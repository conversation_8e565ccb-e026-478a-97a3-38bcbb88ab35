package com.kan.modules.sys.service.impl;

import com.kan.common.exception.KanException;
import com.kan.common.utils.Constant;
import com.kan.modules.sys.entity.SysUserEntity;
import com.kan.modules.sys.service.EmailService;
import com.kan.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邮件服务实现类
 * 注意：由于项目中没有配置Spring Mail，这里提供一个基础实现框架
 * 实际使用时需要添加spring-boot-starter-mail依赖并配置SMTP服务器
 */
@Slf4j
@Service
public class EmailServiceImpl implements EmailService {

    @Autowired
    private SysUserService sysUserService;

    @Value("${spring.mail.from:<EMAIL>}")
    private String fromEmail;

    @Override
    public void sendSimpleEmail(String to, String subject, String content) {
        try {
            // TODO: 实际的邮件发送逻辑
            // 由于项目中没有配置Spring Mail，这里只是记录日志
            log.info("发送邮件 - 收件人: {}, 主题: {}, 内容: {}", to, subject, content);
            
            // 实际实现时应该使用JavaMailSender发送邮件
            // SimpleMailMessage message = new SimpleMailMessage();
            // message.setFrom(fromEmail);
            // message.setTo(to);
            // message.setSubject(subject);
            // message.setText(content);
            // mailSender.send(message);
            
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            throw new KanException("发送邮件失败: " + e.getMessage());
        }
    }

    @Override
    public void sendHtmlEmail(String to, String subject, String content) {
        try {
            // TODO: 实际的HTML邮件发送逻辑
            log.info("发送HTML邮件 - 收件人: {}, 主题: {}, 内容: {}", to, subject, content);
            
            // 实际实现时应该使用MimeMessage发送HTML邮件
            // MimeMessage message = mailSender.createMimeMessage();
            // MimeMessageHelper helper = new MimeMessageHelper(message, true);
            // helper.setFrom(fromEmail);
            // helper.setTo(to);
            // helper.setSubject(subject);
            // helper.setText(content, true);
            // mailSender.send(message);
            
        } catch (Exception e) {
            log.error("发送HTML邮件失败", e);
            throw new KanException("发送HTML邮件失败: " + e.getMessage());
        }
    }

    @Override
    public void sendEmailToAdmin(String subject, String content) {
        try {
            // 获取超级管理员信息
            SysUserEntity adminUser = sysUserService.getById((long) Constant.SUPER_ADMIN);
            if (adminUser != null && adminUser.getEmail() != null) {
                sendSimpleEmail(adminUser.getEmail(), subject, content);
                log.info("已发送邮件给管理员: {}", adminUser.getEmail());
            } else {
                // 如果没有找到管理员邮箱，记录警告日志
                log.warn("未找到管理员邮箱，无法发送邮件。主题: {}, 内容: {}", subject, content);
            }
        } catch (Exception e) {
            log.error("发送邮件给管理员失败", e);
            throw new KanException("发送邮件给管理员失败: " + e.getMessage());
        }
    }
}

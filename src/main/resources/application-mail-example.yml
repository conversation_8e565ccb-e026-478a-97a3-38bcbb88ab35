# 邮件配置示例
# 已在application.yml中配置163邮箱，以下是配置说明

# 当前使用的163邮箱配置：
spring:
  mail:
    # SMTP服务器地址
    host: smtp.163.com
    # SMTP服务器端口
    port: 587
    # 发送者邮箱
    username: <EMAIL>
    # 发送者邮箱授权码（不是登录密码）
    password: UEdAU3BygTGzf8mx
    # 发送者邮箱地址
    from: <EMAIL>
    # 邮件编码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          # 启用SMTP认证
          auth: true
          # 启用STARTTLS加密
          starttls:
            enable: true
            required: true
          # 连接超时时间
          connectiontimeout: 5000
          # 读取超时时间
          timeout: 3000
          # 写入超时时间
          writetimeout: 5000

# 常见邮箱SMTP配置参考：
# QQ邮箱：
#   host: smtp.qq.com
#   port: 587 (STARTTLS) 或 465 (SSL)
#   需要开启SMTP服务并使用授权码

# 163邮箱：
#   host: smtp.163.com
#   port: 587 (STARTTLS) 或 465 (SSL)
#   需要开启SMTP服务并使用授权码

# Gmail：
#   host: smtp.gmail.com
#   port: 587 (STARTTLS) 或 465 (SSL)
#   需要开启两步验证并使用应用专用密码

# 企业邮箱：
#   根据具体的企业邮箱服务商配置
